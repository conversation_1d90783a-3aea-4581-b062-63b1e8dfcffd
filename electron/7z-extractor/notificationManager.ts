import { extractorLogger } from "./logger";
import { Notification, shell } from "electron";
// import * as path from "path";
import type { ExtractionTask } from "./types";

export interface NotificationConfig {
  enabled?: boolean;
  showProgress?: boolean;
  showCompletion?: boolean;
  showErrors?: boolean;
  playSound?: boolean;
  showInTray?: boolean;
  autoHide?: boolean;
  autoHideDelay?: number; // 毫秒
}

export interface NotificationData {
  title: string;
  body: string;
  icon?: string;
  silent?: boolean;
  actions?: Array<{
    type: string;
    text: string;
  }>;
  tag?: string;
  urgency?: "normal" | "critical" | "low";
}

export class ExtractionNotificationManager {
  private config: NotificationConfig;
  private activeNotifications: Map<string, Notification> = new Map();
  private notificationHistory: Array<{
    taskId: string;
    type: string;
    timestamp: Date;
    data: NotificationData;
  }> = [];

  constructor(config: NotificationConfig = {}) {
    this.config = {
      enabled: true,
      showProgress: false, // 进度通知可能太频繁
      showCompletion: true,
      showErrors: true,
      playSound: true,
      showInTray: true,
      autoHide: true,
      autoHideDelay: 5000,
      ...config,
    };
  }

  /**
   * 显示解压缩开始通知
   */
  showExtractionStarted(task: ExtractionTask): void {
    if (!this.config.enabled) return;

    const notification = this.createNotification({
      title: "开始解压缩",
      body: `正在解压缩 "${task.fileName}"`,
      icon: this.getIcon("start"),
      silent: !this.config.playSound,
      tag: `extraction-start-${task.id}`,
    });

    this.showNotification(`start-${task.id}`, notification, task.id, "start");
  }

  /**
   * 显示解压缩进度通知
   */
  showExtractionProgress(task: ExtractionTask, progress: number): void {
    if (!this.config.enabled || !this.config.showProgress) return;

    // 只在特定进度点显示通知，避免过于频繁
    if (progress % 25 !== 0) return;

    const notification = this.createNotification({
      title: "解压缩进度",
      body: `"${task.fileName}" 已完成 ${progress}%`,
      icon: this.getIcon("progress"),
      silent: true, // 进度通知静音
      tag: `extraction-progress-${task.id}`,
    });

    this.showNotification(`progress-${task.id}`, notification, task.id, "progress");
  }

  /**
   * 显示解压缩完成通知
   */
  showExtractionCompleted(task: ExtractionTask): void {
    if (!this.config.enabled || !this.config.showCompletion) return;

    const notification = this.createNotification({
      title: "解压缩完成",
      body: `"${task.fileName}" 已成功解压缩`,
      icon: this.getIcon("success"),
      silent: !this.config.playSound,
      actions: [
        { type: "open", text: "打开文件夹" },
        { type: "dismiss", text: "关闭" },
      ],
      tag: `extraction-completed-${task.id}`,
    });

    // 设置点击事件
    notification.on("click", () => {
      this.openExtractedFolder(task.extractPath);
    });

    notification.on("action", (_event, index) => {
      if (index === 0) {
        // 打开文件夹
        this.openExtractedFolder(task.extractPath);
      }
      notification.close();
    });

    this.showNotification(`completed-${task.id}`, notification, task.id, "completed");
  }

  /**
   * 显示解压缩错误通知
   */
  showExtractionError(task: ExtractionTask, error: string): void {
    if (!this.config.enabled || !this.config.showErrors) return;

    const notification = this.createNotification({
      title: "解压缩失败",
      body: `"${task.fileName}" 解压缩失败: ${error}`,
      icon: this.getIcon("error"),
      silent: !this.config.playSound,
      urgency: "critical",
      actions: [
        { type: "retry", text: "重试" },
        { type: "dismiss", text: "关闭" },
      ],
      tag: `extraction-error-${task.id}`,
    });

    notification.on("action", (_event, index) => {
      if (index === 0) {
        // 重试
        // 触发重试事件
        this.emit("retry-requested", task.id);
      }
      notification.close();
    });

    this.showNotification(`error-${task.id}`, notification, task.id, "error");
  }

  /**
   * 显示密码需求通知
   */
  showPasswordRequired(task: ExtractionTask): void {
    if (!this.config.enabled) return;

    const notification = this.createNotification({
      title: "需要密码",
      body: `"${task.fileName}" 需要密码才能解压缩`,
      icon: this.getIcon("password"),
      silent: !this.config.playSound,
      urgency: "critical",
      actions: [
        { type: "password", text: "输入密码" },
        { type: "skip", text: "跳过" },
      ],
      tag: `extraction-password-${task.id}`,
    });

    notification.on("action", (_event, index) => {
      if (index === 0) {
        // 输入密码
        this.emit("password-requested", task.id);
      } else {
        // 跳过
        this.emit("password-skipped", task.id);
      }
      notification.close();
    });

    this.showNotification(`password-${task.id}`, notification, task.id, "password");
  }

  /**
   * 显示批量解压缩完成通知
   */
  showBatchExtractionCompleted(completedCount: number, _totalCount: number, failedCount: number = 0): void {
    if (!this.config.enabled || !this.config.showCompletion) return;

    let body: string;
    let icon: string;

    if (failedCount === 0) {
      body = `已成功解压缩 ${completedCount} 个文件`;
      icon = this.getIcon("success");
    } else {
      body = `解压缩完成: ${completedCount} 个成功, ${failedCount} 个失败`;
      icon = this.getIcon("warning");
    }

    const notification = this.createNotification({
      title: "批量解压缩完成",
      body,
      icon,
      silent: !this.config.playSound,
      tag: "batch-extraction-completed",
    });

    this.showNotification("batch-completed", notification, "batch", "batch-completed");
  }

  /**
   * 创建通知对象
   */
  private createNotification(data: NotificationData): Notification {
    const notification = new Notification({
      title: data.title,
      body: data.body,
      icon: data.icon,
      silent: data.silent || false,
      urgency: data.urgency || "normal",
      // tag: data.tag, // tag属性在Electron的Notification中不支持
    });

    // 设置自动隐藏
    if (this.config.autoHide && this.config.autoHideDelay) {
      setTimeout(() => {
        try {
          notification.close();
        } catch (error) {
          // 通知可能已经被销毁，忽略错误
        }
      }, this.config.autoHideDelay);
    }

    return notification;
  }

  /**
   * 显示通知
   */
  private showNotification(notificationId: string, notification: Notification, taskId: string, type: string): void {
    // 关闭之前的同类型通知
    const existingNotification = this.activeNotifications.get(notificationId);
    if (existingNotification) {
      try {
        existingNotification.close();
      } catch (error) {
        // 通知可能已经被销毁，忽略错误
      }
    }

    // 显示新通知
    notification.show();
    this.activeNotifications.set(notificationId, notification);

    // 记录通知历史
    this.notificationHistory.push({
      taskId,
      type,
      timestamp: new Date(),
      data: {
        title: notification.title,
        body: notification.body,
      },
    });

    // 设置通知关闭事件
    notification.on("close", () => {
      this.activeNotifications.delete(notificationId);
    });

    // 限制历史记录数量
    if (this.notificationHistory.length > 100) {
      this.notificationHistory = this.notificationHistory.slice(-50);
    }
  }

  /**
   * 获取图标路径
   */
  private getIcon(type: string): string {
    // 这里应该返回实际的图标路径
    // 可以根据应用的图标资源来设置
    const iconMap: Record<string, string> = {
      start: "extraction-start.png",
      progress: "extraction-progress.png",
      success: "extraction-success.png",
      error: "extraction-error.png",
      warning: "extraction-warning.png",
      password: "extraction-password.png",
    };

    return iconMap[type] || "extraction-default.png";
  }

  /**
   * 打开解压缩文件夹
   */
  private openExtractedFolder(extractPath: string): void {
    try {
      shell.openPath(extractPath);
    } catch (error) {
      extractorLogger.error("打开文件夹失败:", error);
    }
  }

  /**
   * 关闭指定任务的所有通知
   */
  closeTaskNotifications(taskId: string): void {
    const notificationsToClose = Array.from(this.activeNotifications.entries()).filter(([id]) => id.includes(taskId));

    notificationsToClose.forEach(([id, notification]) => {
      try {
        notification.close();
      } catch (error) {
        // 通知可能已经被销毁，忽略错误
      }
      this.activeNotifications.delete(id);
    });
  }

  /**
   * 关闭所有通知
   */
  closeAllNotifications(): void {
    this.activeNotifications.forEach((notification, _id) => {
      try {
        notification.close();
      } catch (error) {
        // 通知可能已经被销毁，忽略错误
      }
    });
    this.activeNotifications.clear();
  }

  /**
   * 获取通知历史
   */
  getNotificationHistory(): Array<{
    taskId: string;
    type: string;
    timestamp: Date;
    data: NotificationData;
  }> {
    return [...this.notificationHistory];
  }

  /**
   * 清空通知历史
   */
  clearNotificationHistory(): void {
    this.notificationHistory = [];
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<NotificationConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 获取当前配置
   */
  getConfig(): NotificationConfig {
    return { ...this.config };
  }

  /**
   * 检查通知权限
   */
  static async checkPermission(): Promise<boolean> {
    try {
      // 在 Electron 中，通知权限通常是自动授予的
      return true;
    } catch (error) {
      extractorLogger.error("检查通知权限失败:", error);
      return false;
    }
  }

  /**
   * 事件发射器方法（简化版）
   */
  private emit(event: string, ...args: any[]): void {
    // 这里应该使用 EventEmitter 或其他事件系统
    extractorLogger.info(`通知事件: ${event}`, args);
  }
}
