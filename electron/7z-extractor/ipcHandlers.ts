import { extractorLogger } from "./logger";
import { ipcMain, dialog } from "electron";
import type { SevenZipExtractionManager } from "./extractionManager";
import type { ExtractionApiResponse } from "./types";

export function registerExtractionIpcHandlers(extractionManager: SevenZipExtractionManager) {
  // 创建解压缩任务
  ipcMain.handle(
    "extraction-create-task",
    async (
      _event,
      archivePath: string,
      extractPath?: string,
      options?: {
        downloadTaskId?: string;
        deleteAfterExtraction?: boolean;
        password?: string;
      }
    ): Promise<ExtractionApiResponse> => {
      try {
        const taskId = await extractionManager.createExtractionTask(archivePath, extractPath, options);
        return { success: true, taskId };
      } catch (error) {
        return { success: false, error: String(error) };
      }
    }
  );

  // 通过对话框选择解压路径创建任务
  ipcMain.handle(
    "extraction-create-task-with-dialog",
    async (
      _event,
      archivePath: string,
      options?: {
        downloadTaskId?: string;
        deleteAfterExtraction?: boolean;
        password?: string;
      }
    ): Promise<ExtractionApiResponse> => {
      try {
        const result = await dialog.showOpenDialog({
          title: "选择解压目标文件夹",
          properties: ["openDirectory", "createDirectory"],
        });

        if (result.canceled || !result.filePaths.length) {
          return { success: false, error: "用户取消了文件夹选择" };
        }

        const extractPath = result.filePaths[0];
        const taskId = await extractionManager.createExtractionTask(archivePath, extractPath, options);
        return { success: true, taskId };
      } catch (error) {
        return { success: false, error: String(error) };
      }
    }
  );

  // 开始解压缩
  ipcMain.handle("extraction-start", async (_event, taskId: string): Promise<ExtractionApiResponse> => {
    try {
      await extractionManager.startExtraction(taskId);
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 暂停解压缩
  ipcMain.handle("extraction-pause", async (_event, taskId: string): Promise<ExtractionApiResponse> => {
    try {
      await extractionManager.pauseExtraction(taskId);
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 恢复解压缩
  ipcMain.handle("extraction-resume", async (_event, taskId: string): Promise<ExtractionApiResponse> => {
    try {
      await extractionManager.resumeExtraction(taskId);
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 取消解压缩
  ipcMain.handle("extraction-cancel", async (_event, taskId: string): Promise<ExtractionApiResponse> => {
    try {
      await extractionManager.cancelExtraction(taskId);
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 删除解压缩任务
  ipcMain.handle("extraction-delete-task", async (_event, taskId: string): Promise<ExtractionApiResponse> => {
    try {
      await extractionManager.deleteTask(taskId);
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 获取所有解压缩任务
  ipcMain.handle("extraction-get-all-tasks", async (_event): Promise<ExtractionApiResponse> => {
    try {
      const tasks = extractionManager.getAllTasks();
      return { success: true, tasks };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 获取指定解压缩任务
  ipcMain.handle("extraction-get-task", async (_event, taskId: string): Promise<ExtractionApiResponse> => {
    try {
      const task = extractionManager.getTask(taskId);
      if (!task) {
        return { success: false, error: "任务不存在" };
      }
      return { success: true, task };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 获取活跃解压缩任务
  ipcMain.handle("extraction-get-active-tasks", async (_event): Promise<ExtractionApiResponse> => {
    try {
      const tasks = extractionManager.getActiveTasks();
      return { success: true, tasks };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 清理已完成的解压缩任务
  ipcMain.handle("extraction-clear-completed-tasks", async (_event): Promise<ExtractionApiResponse> => {
    try {
      extractionManager.clearCompletedTasks();
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 清空所有解压缩任务
  ipcMain.handle("extraction-clear-all-tasks", async (_event): Promise<ExtractionApiResponse> => {
    try {
      await extractionManager.clearAllTasks();
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 批量开始解压缩
  ipcMain.handle("extraction-start-batch", async (_event, taskIds: string[]): Promise<ExtractionApiResponse> => {
    try {
      const results: { taskId: string; success: boolean; error?: string }[] = [];

      for (const taskId of taskIds) {
        try {
          await extractionManager.startExtraction(taskId);
          results.push({ taskId, success: true });
        } catch (error) {
          results.push({ taskId, success: false, error: String(error) });
        }
      }

      return { success: true, data: { results } };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 批量暂停解压缩
  ipcMain.handle("extraction-pause-batch", async (_event, taskIds: string[]): Promise<ExtractionApiResponse> => {
    try {
      const results: { taskId: string; success: boolean; error?: string }[] = [];

      for (const taskId of taskIds) {
        try {
          await extractionManager.pauseExtraction(taskId);
          results.push({ taskId, success: true });
        } catch (error) {
          results.push({ taskId, success: false, error: String(error) });
        }
      }

      return { success: true, data: { results } };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 批量恢复解压缩
  ipcMain.handle("extraction-resume-batch", async (_event, taskIds: string[]): Promise<ExtractionApiResponse> => {
    try {
      const results: { taskId: string; success: boolean; error?: string }[] = [];

      for (const taskId of taskIds) {
        try {
          await extractionManager.resumeExtraction(taskId);
          results.push({ taskId, success: true });
        } catch (error) {
          results.push({ taskId, success: false, error: String(error) });
        }
      }

      return { success: true, data: { results } };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 批量取消解压缩
  ipcMain.handle("extraction-cancel-batch", async (_event, taskIds: string[]): Promise<ExtractionApiResponse> => {
    try {
      const results: { taskId: string; success: boolean; error?: string }[] = [];

      for (const taskId of taskIds) {
        try {
          await extractionManager.cancelExtraction(taskId);
          results.push({ taskId, success: true });
        } catch (error) {
          results.push({ taskId, success: false, error: String(error) });
        }
      }

      return { success: true, data: { results } };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 提供密码
  ipcMain.handle("extraction-provide-password", async (_event, taskId: string, password: string): Promise<ExtractionApiResponse> => {
    try {
      const task = extractionManager.getTask(taskId);
      if (!task) {
        return { success: false, error: "任务不存在" };
      }

      // 更新任务密码并重新开始解压缩
      task.password = password;
      await extractionManager.startExtraction(taskId);

      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 处理文件冲突
  ipcMain.handle("extraction-handle-file-conflict", async (_event, _taskId: string, action: "overwrite" | "skip" | "rename"): Promise<ExtractionApiResponse> => {
    try {
      // 这里需要根据具体的冲突处理逻辑来实现
      // 暂时返回成功，实际实现需要更复杂的逻辑
      return { success: true, data: { action } };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  extractorLogger.info("✅ 解压缩 IPC 处理器注册完成");
}
