// import * as fs from "fs";
// import * as path from "path";
import { dialog } from "electron";
import type { ExtractionTask, FileConflictAction } from "./types";
import { extractorLogger } from "./logger";

export interface ErrorHandlerConfig {
  autoRetryCount?: number;
  retryDelays?: number[]; // 重试延迟时间（毫秒）
  showUserPrompts?: boolean;
  logErrors?: boolean;
}

export interface ErrorContext {
  taskId: string;
  task: ExtractionTask;
  error: Error | string;
  retryCount: number;
  timestamp: Date;
}

export interface ConflictResolution {
  action: FileConflictAction;
  applyToAll?: boolean;
  newName?: string;
}

export class ExtractionErrorHandler {
  private config: ErrorHandlerConfig;
  private retryAttempts: Map<string, number> = new Map();
  private conflictResolutions: Map<string, ConflictResolution> = new Map();

  constructor(config: ErrorHandlerConfig = {}) {
    this.config = {
      autoRetryCount: 3,
      retryDelays: [1000, 3000, 5000],
      showUserPrompts: true,
      logErrors: true,
      ...config,
    };
  }

  /**
   * 处理解压缩错误
   */
  async handleError(context: ErrorContext): Promise<{
    shouldRetry: boolean;
    delay?: number;
    newPassword?: string;
    resolution?: ConflictResolution;
  }> {
    const { taskId, task, error, retryCount } = context;
    const errorMessage = error instanceof Error ? error.message : String(error);

    if (this.config.logErrors) {
      extractorLogger.error(`解压缩错误 [${taskId}]: ${errorMessage}`, {
        fileName: task.fileName,
        retryCount,
        timestamp: context.timestamp,
      });
    }

    // 检查错误类型并决定处理策略
    if (this.isPasswordError(errorMessage)) {
      return this.handlePasswordError(context);
    }

    if (this.isFileConflictError(errorMessage)) {
      return this.handleFileConflictError(context);
    }

    if (this.isDiskSpaceError(errorMessage)) {
      return this.handleDiskSpaceError(context);
    }

    if (this.isCorruptedFileError(errorMessage)) {
      return this.handleCorruptedFileError(context);
    }

    if (this.isPermissionError(errorMessage)) {
      return this.handlePermissionError(context);
    }

    // 通用错误处理
    return this.handleGenericError(context);
  }

  /**
   * 处理密码错误
   */
  private async handlePasswordError(context: ErrorContext): Promise<{
    shouldRetry: boolean;
    newPassword?: string;
  }> {
    const { task } = context;

    if (!this.config.showUserPrompts) {
      return { shouldRetry: false };
    }

    try {
      // 显示密码输入对话框
      const result = await dialog.showMessageBox({
        type: "question",
        title: "需要密码",
        message: `文件 "${task.fileName}" 需要密码才能解压缩`,
        detail: "请输入正确的解压密码",
        buttons: ["输入密码", "跳过"],
        defaultId: 0,
        cancelId: 1,
      });

      if (result.response === 0) {
        // 用户选择输入密码
        // 这里应该触发密码输入事件，让前端处理
        return { shouldRetry: true };
      } else {
        return { shouldRetry: false };
      }
    } catch (error) {
      extractorLogger.error("显示密码对话框失败:", error);
      return { shouldRetry: false };
    }
  }

  /**
   * 处理文件冲突错误
   */
  private async handleFileConflictError(context: ErrorContext): Promise<{
    shouldRetry: boolean;
    resolution?: ConflictResolution;
  }> {
    const { taskId } = context;

    // 检查是否已有冲突解决方案
    const existingResolution = this.conflictResolutions.get(taskId);
    if (existingResolution?.applyToAll) {
      return { shouldRetry: true, resolution: existingResolution };
    }

    if (!this.config.showUserPrompts) {
      // 默认覆盖
      return {
        shouldRetry: true,
        resolution: { action: "overwrite", applyToAll: true },
      };
    }

    try {
      const result = await dialog.showMessageBox({
        type: "question",
        title: "文件冲突",
        message: `解压时发现文件冲突`,
        detail: `目标位置已存在同名文件，请选择处理方式`,
        buttons: ["覆盖", "跳过", "重命名", "取消"],
        defaultId: 0,
        cancelId: 3,
      });

      let action: FileConflictAction;
      switch (result.response) {
        case 0:
          action = "overwrite";
          break;
        case 1:
          action = "skip";
          break;
        case 2:
          action = "rename";
          break;
        default:
          return { shouldRetry: false };
      }

      const resolution: ConflictResolution = { action, applyToAll: true };
      this.conflictResolutions.set(taskId, resolution);

      return { shouldRetry: true, resolution };
    } catch (error) {
      extractorLogger.error("显示文件冲突对话框失败:", error);
      return { shouldRetry: false };
    }
  }

  /**
   * 处理磁盘空间不足错误
   */
  private async handleDiskSpaceError(context: ErrorContext): Promise<{
    shouldRetry: boolean;
  }> {
    const { task } = context;

    if (!this.config.showUserPrompts) {
      return { shouldRetry: false };
    }

    try {
      const result = await dialog.showMessageBox({
        type: "error",
        title: "磁盘空间不足",
        message: `解压缩 "${task.fileName}" 时磁盘空间不足`,
        detail: "请释放磁盘空间后重试，或选择其他位置解压",
        buttons: ["重试", "选择其他位置", "取消"],
        defaultId: 0,
        cancelId: 2,
      });

      if (result.response === 0) {
        return { shouldRetry: true };
      } else if (result.response === 1) {
        // 用户选择其他位置，这里应该触发路径选择事件
        return { shouldRetry: false };
      } else {
        return { shouldRetry: false };
      }
    } catch (error) {
      extractorLogger.error("显示磁盘空间对话框失败:", error);
      return { shouldRetry: false };
    }
  }

  /**
   * 处理文件损坏错误
   */
  private async handleCorruptedFileError(context: ErrorContext): Promise<{
    shouldRetry: boolean;
  }> {
    const { task } = context;

    if (!this.config.showUserPrompts) {
      return { shouldRetry: false };
    }

    try {
      await dialog.showMessageBox({
        type: "error",
        title: "文件损坏",
        message: `文件 "${task.fileName}" 已损坏，无法解压缩`,
        detail: "请检查文件是否完整下载或重新下载",
        buttons: ["确定"],
      });

      return { shouldRetry: false };
    } catch (error) {
      extractorLogger.error("显示文件损坏对话框失败:", error);
      return { shouldRetry: false };
    }
  }

  /**
   * 处理权限错误
   */
  private async handlePermissionError(context: ErrorContext): Promise<{
    shouldRetry: boolean;
  }> {
    const { task } = context;

    if (!this.config.showUserPrompts) {
      return { shouldRetry: false };
    }

    try {
      const result = await dialog.showMessageBox({
        type: "error",
        title: "权限不足",
        message: `没有权限访问目标位置`,
        detail: `请检查对 "${task.extractPath}" 的写入权限`,
        buttons: ["重试", "选择其他位置", "取消"],
        defaultId: 0,
        cancelId: 2,
      });

      if (result.response === 0) {
        return { shouldRetry: true };
      } else {
        return { shouldRetry: false };
      }
    } catch (error) {
      extractorLogger.error("显示权限错误对话框失败:", error);
      return { shouldRetry: false };
    }
  }

  /**
   * 处理通用错误
   */
  private async handleGenericError(context: ErrorContext): Promise<{
    shouldRetry: boolean;
    delay?: number;
  }> {
    const { retryCount } = context;
    const maxRetries = this.config.autoRetryCount || 3;

    if (retryCount < maxRetries) {
      const delay = this.config.retryDelays?.[retryCount] || 1000;
      extractorLogger.info(`将在 ${delay}ms 后重试 (${retryCount + 1}/${maxRetries})`);
      return { shouldRetry: true, delay };
    }

    return { shouldRetry: false };
  }

  /**
   * 检查是否为密码错误
   */
  private isPasswordError(errorMessage: string): boolean {
    const passwordKeywords = ["wrong password", "password", "密码", "encrypted", "加密", "cannot open encrypted archive"];

    return passwordKeywords.some((keyword) => errorMessage.toLowerCase().includes(keyword.toLowerCase()));
  }

  /**
   * 检查是否为文件冲突错误
   */
  private isFileConflictError(errorMessage: string): boolean {
    const conflictKeywords = ["file exists", "already exists", "文件已存在", "overwrite", "覆盖"];

    return conflictKeywords.some((keyword) => errorMessage.toLowerCase().includes(keyword.toLowerCase()));
  }

  /**
   * 检查是否为磁盘空间错误
   */
  private isDiskSpaceError(errorMessage: string): boolean {
    const spaceKeywords = ["no space left", "disk full", "insufficient space", "磁盘空间不足", "空间不够"];

    return spaceKeywords.some((keyword) => errorMessage.toLowerCase().includes(keyword.toLowerCase()));
  }

  /**
   * 检查是否为文件损坏错误
   */
  private isCorruptedFileError(errorMessage: string): boolean {
    const corruptedKeywords = ["corrupted", "damaged", "invalid archive", "文件损坏", "无效的压缩包", "crc error"];

    return corruptedKeywords.some((keyword) => errorMessage.toLowerCase().includes(keyword.toLowerCase()));
  }

  /**
   * 检查是否为权限错误
   */
  private isPermissionError(errorMessage: string): boolean {
    const permissionKeywords = ["permission denied", "access denied", "权限不足", "拒绝访问", "cannot create"];

    return permissionKeywords.some((keyword) => errorMessage.toLowerCase().includes(keyword.toLowerCase()));
  }

  /**
   * 重置任务的重试计数
   */
  resetRetryCount(taskId: string): void {
    this.retryAttempts.delete(taskId);
  }

  /**
   * 获取任务的重试次数
   */
  getRetryCount(taskId: string): number {
    return this.retryAttempts.get(taskId) || 0;
  }

  /**
   * 增加任务的重试次数
   */
  incrementRetryCount(taskId: string): number {
    const current = this.getRetryCount(taskId);
    const newCount = current + 1;
    this.retryAttempts.set(taskId, newCount);
    return newCount;
  }

  /**
   * 清理任务相关的错误处理数据
   */
  cleanupTask(taskId: string): void {
    this.retryAttempts.delete(taskId);
    this.conflictResolutions.delete(taskId);
  }
}
