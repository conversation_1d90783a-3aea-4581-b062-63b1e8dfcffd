# FolderPicker 文件夹选择器组件

文件夹选择器组件提供了一个树形结构的文件夹选择界面，支持懒加载和递归展开。

## 组件结构

```
src/components/FolderPicker/
├── index.ts                 # 组件导出入口
├── FolderPickerDialog.vue   # 主对话框组件
├── FolderTreeNode.vue       # 树节点组件
└── README.md               # 使用文档
```

## 使用方法

### 基本用法

```vue
<template>
  <div>
    <Button @click="showPicker = true">选择文件夹</Button>
    
    <FolderPickerDialog
      v-model:open="showPicker"
      :category-id="categoryId"
      :current-parent-id="currentParentId"
      @confirm="handleFolderSelected"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { FolderPickerDialog } from '@/components/FolderPicker'

const showPicker = ref(false)
const categoryId = ref('1')
const currentParentId = ref('1')

const handleFolderSelected = (targetDirId: string) => {
  console.log('选择的文件夹ID:', targetDirId)
  showPicker.value = false
}

const handleCancel = () => {
  showPicker.value = false
}
</script>
```

### 与 useFileMove 组合使用

```vue
<template>
  <div>
    <Button @click="handleMove">移动文件</Button>
    
    <FolderPickerDialog
      v-model:open="showFolderPicker"
      :category-id="categoryId"
      :current-parent-id="currentParentId"
      @confirm="handleFolderSelected"
      @cancel="cancelMove"
    />
  </div>
</template>

<script setup lang="ts">
import { useFileMove } from '@/composables/useFileMove'
import { FolderPickerDialog } from '@/components/FolderPicker'

const {
  isMoving,
  showFolderPicker,
  startMove,
  handleFolderSelected,
  cancelMove
} = useFileMove()

const handleMove = () => {
  startMove(selectedItems.value)
}
</script>
```

## API 参考

### FolderPickerDialog Props

| 属性 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| `open` | `boolean` | ✅ | - | 对话框是否打开 |
| `categoryId` | `string` | ✅ | - | 分类ID |
| `currentParentId` | `string` | ❌ | - | 当前父级文件夹ID（用于禁用选择） |

### FolderPickerDialog Events

| 事件 | 参数 | 描述 |
|------|------|------|
| `update:open` | `(value: boolean)` | 对话框打开状态变化 |
| `confirm` | `(targetDirId: string)` | 确认选择文件夹 |
| `cancel` | `()` | 取消选择 |

### TreeNode 接口

```typescript
interface TreeNode {
  id: string              // 节点ID
  name: string            // 节点名称
  hasChildren: boolean    // 是否有子节点
  isExpanded: boolean     // 是否展开
  isLoading: boolean      // 是否正在加载
  children: TreeNode[]    // 子节点数组
  itemCount?: number      // 项目数量（可选）
  parentId?: string       // 父节点ID（可选）
}
```

## 特性

- ✅ **树形结构**: 直观的文件夹层级显示
- ✅ **懒加载**: 只在展开时加载子文件夹，提高性能
- ✅ **缓存机制**: 避免重复的API调用
- ✅ **递归展开**: 支持任意深度的文件夹展开
- ✅ **禁用逻辑**: 防止选择当前父级文件夹
- ✅ **加载状态**: 清晰的加载和空状态提示
- ✅ **TypeScript**: 完整的类型支持