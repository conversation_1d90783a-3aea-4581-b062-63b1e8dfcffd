/**
 * 上传相关工具函数
 */

// 任务状态类型
export type TaskStatus = "pending" | "uploading" | "paused" | "completed" | "error" | "cancelled";

// 文件大小格式化
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 B";

  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
}

// 上传速度格式化
export function formatUploadSpeed(bytesPerSecond: number): string {
  if (bytesPerSecond === 0) return "0 B/s";

  const k = 1024;
  const sizes = ["B/s", "KB/s", "MB/s", "GB/s"];
  const i = Math.floor(Math.log(bytesPerSecond) / Math.log(k));

  return `${parseFloat((bytesPerSecond / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
}

// 剩余时间格式化
export function formatRemainingTime(seconds: number): string {
  if (seconds < 60) {
    return `${Math.round(seconds)}秒`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    return `${minutes}分钟`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}小时${minutes}分钟`;
  }
}

// 持续时间格式化
export function formatDuration(duration: number): string {
  const seconds = Math.floor(duration / 1000);
  if (seconds < 60) {
    return `${seconds}秒`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    return `${minutes}分钟`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return hours > 0 ? `${hours}小时${minutes}分钟` : `${minutes}分钟`;
  }
}

// 日期格式化
export function formatDate(date: Date): string {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const taskDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

  if (taskDate.getTime() === today.getTime()) {
    return date.toLocaleTimeString("zh-CN", {
      hour: "2-digit",
      minute: "2-digit",
    });
  } else {
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    if (taskDate.getTime() === yesterday.getTime()) {
      return (
        "昨天 " +
        date.toLocaleTimeString("zh-CN", {
          hour: "2-digit",
          minute: "2-digit",
        })
      );
    } else {
      return (
        date.toLocaleDateString("zh-CN", {
          month: "short",
          day: "numeric",
        }) +
        " " +
        date.toLocaleTimeString("zh-CN", {
          hour: "2-digit",
          minute: "2-digit",
        })
      );
    }
  }
}

// 获取状态样式类
export function getStatusClass(status: TaskStatus): string {
  switch (status) {
    case "completed":
      return "text-green-600";
    case "error":
      return "text-red-600";
    case "uploading":
      return "text-blue-600";
    case "paused":
      return "text-yellow-600";
    case "cancelled":
      return "text-gray-500";
    default:
      return "text-gray-600";
  }
}

// 获取状态文本
export function getStatusText(status: TaskStatus): string {
  switch (status) {
    case "completed":
      return "已完成";
    case "error":
      return "失败";
    case "uploading":
      return "上传中";
    case "paused":
      return "暂停";
    case "pending":
      return "等待";
    case "cancelled":
      return "已取消";
    default:
      return "未知状态";
  }
}

// 获取进度条样式类
export function getProgressClass(status: TaskStatus): string {
  switch (status) {
    case "completed":
      return "bg-green-500";
    case "error":
      return "bg-red-500";
    case "uploading":
      return "bg-blue-500";
    case "paused":
      return "bg-yellow-500";
    default:
      return "bg-gray-500";
  }
}

// 生成批量任务名称
export function generateBatchName(): string {
  const timestamp = new Date().toLocaleString("zh-CN", {
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
  return `批量上传_${timestamp}`;
}

// 创建上传元数据
export function createUploadMetadata(attributes: Record<string, string>, uploadType: "single" | "batch" | "folder", additionalData?: Record<string, any>): Record<string, any> {
  return {
    ...attributes,
    uploadType,
    uploadBatch: Date.now().toString(),
    ...additionalData,
  };
}

// 上传策略类型 - 重新设计支持文件结构
export interface UploadStrategy {
  type: "single" | "batch" | "folder" | "mixed";
  folders: Array<{ name: string; files: File[]; path: string }>;
  singleFiles: File[];
  description: string; // 策略描述，用于调试和用户提示
}

/**
 * 跨平台路径处理工具函数
 */
function normalizePathSeparators(path: string): string {
  // 将所有反斜杠转换为正斜杠，统一使用 Unix 风格的路径分隔符
  return path.replace(/\\/g, "/");
}

function hasPathSeparator(path: string): boolean {
  // 检查路径是否包含分隔符（支持 Windows 和 Unix 风格）
  return path.includes("/") || path.includes("\\");
}

function getTopLevelFolder(relativePath: string): string {
  // 统一路径分隔符后获取顶级文件夹名称
  const normalizedPath = normalizePathSeparators(relativePath);
  const pathParts = normalizedPath.split("/");
  return pathParts[0];
}

// 检测文件列表的上传策略 - 重新设计支持文件结构保持
export function detectUploadStrategy(files: File[]): UploadStrategy {
  if (files.length === 0) {
    return {
      type: "single",
      folders: [],
      singleFiles: [],
      description: "无文件",
    };
  }

  const folders: Array<{ name: string; files: File[]; path: string }> = [];
  const singleFiles: File[] = [];

  // 检查是否有真正的文件夹结构（webkitRelativePath 包含路径分隔符）
  // 支持 Windows (\) 和 Unix (/) 两种路径分隔符
  const hasRealFolderStructure = files.some((file) => {
    const relativePath = (file as any).webkitRelativePath as string;
    return relativePath && hasPathSeparator(relativePath);
  });

  if (hasRealFolderStructure) {
    // 按文件夹路径分组 - 支持多级目录和跨平台路径分隔符
    const folderMap = new Map<string, { files: File[]; path: string }>();

    files.forEach((file) => {
      const relativePath = (file as any).webkitRelativePath as string;
      if (relativePath && hasPathSeparator(relativePath)) {
        // 获取顶级文件夹名称（支持跨平台路径分隔符）
        const topLevelFolder = getTopLevelFolder(relativePath);

        if (!folderMap.has(topLevelFolder)) {
          folderMap.set(topLevelFolder, {
            files: [],
            path: topLevelFolder,
          });
        }
        folderMap.get(topLevelFolder)!.files.push(file);
      } else {
        // 非文件夹文件当作单独文件处理
        singleFiles.push(file);
      }
    });

    // 转换为 folders 数组
    folderMap.forEach((folderData, folderName) => {
      folders.push({
        name: folderName,
        files: folderData.files,
        path: folderData.path,
      });
    });

    // 策略判断 - 根据实际需求重新设计：

    // 1. 只有一个文件夹且没有单独文件 - 单文件夹上传
    if (folders.length === 1 && singleFiles.length === 0) {
      const folder = folders[0];
      return {
        type: "folder",
        folders,
        singleFiles: [],
        description: `单文件夹上传: ${folder.name} (${folder.files.length}个文件)`,
      };
    }

    // 2. 多个文件夹（可能包含单独文件） - 混合结构上传
    if (folders.length > 1) {
      const totalFolders = folders.length;
      const totalFiles = singleFiles.length;
      return {
        type: "mixed",
        folders,
        singleFiles,
        description: `混合结构上传: ${totalFolders}个文件夹, ${totalFiles}个单独文件`,
      };
    }

    // 3. 有文件夹和单独文件的混合情况
    if (folders.length === 1 && singleFiles.length > 0) {
      const folder = folders[0];
      return {
        type: "mixed",
        folders,
        singleFiles,
        description: `混合上传: 文件夹${folder.name}(${folder.files.length}个文件) + ${singleFiles.length}个单独文件`,
      };
    }
  }

  // 普通文件选择的情况
  if (files.length === 1) {
    return {
      type: "single",
      folders: [],
      singleFiles: [files[0]],
      description: `单文件上传: ${files[0].name}`,
    };
  } else if (files.length > 1) {
    return {
      type: "batch",
      folders: [],
      singleFiles: files,
      description: `批量文件上传: ${files.length}个文件`,
    };
  }

  return {
    type: "single",
    folders: [],
    singleFiles: [],
    description: "无效策略",
  };
}

// 错误处理工具
export function handleUploadError(error: unknown, context: string): string {
  const errorMessage = error instanceof Error ? error.message : "未知错误";
  console.error(`${context}:`, error);
  return errorMessage;
}
