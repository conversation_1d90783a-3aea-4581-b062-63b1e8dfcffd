import { ref, computed } from "vue";
import { useRoute } from "vue-router";
import { toast } from "vue-sonner";
import filesApi, { type IFileOperationData } from "@/api/services/files";
import type { ItemType } from "@/types/files";

export function useFileDelete() {
  // 实时获取当前路由的 categoryId
  const route = useRoute();
  const categoryId = computed(() => (route.query.categoryId as string) || "");
  const isDeleting = ref(false);

  // 调用删除接口（统一方法）
  const callDeleteAPI = async (fileIds: (number | string)[]): Promise<{ success: boolean; message: string }> => {
    try {
      const deleteData: IFileOperationData = {
        category_id: categoryId.value,
        file_ids: fileIds,
      };

      const response = await filesApi.deleteFile(deleteData);

      if (response.code === 0) {
        // 成功时使用后端返回的消息，如果没有则使用默认消息
        return { success: true, message: response.msg || "删除成功" };
      } else {
        // 失败时使用后端返回的消息，如果没有则使用默认消息
        return { success: false, message: response.msg || "删除失败" };
      }
    } catch (error) {
      console.error("删除API调用失败:", error);
      const errorMessage = error instanceof Error ? error.message : "删除失败";
      return { success: false, message: errorMessage };
    }
  };

  // 删除单个文件
  const deleteSingleFile = async (item: ItemType): Promise<boolean> => {
    if (isDeleting.value) return false;

    // 确认删除
    const confirmed = confirm(`确定要删除 "${item.name}" 吗？此操作不可撤销。`);
    if (!confirmed) return false;

    isDeleting.value = true;

    try {
      const result = await callDeleteAPI([item.id]);

      if (result.success) {
        // 使用后端返回的成功消息，如果没有则使用默认格式
        const successMessage = result.message || `已删除 "${item.name}"`;
        toast.success(successMessage);
        return true;
      } else {
        // 使用后端返回的错误消息
        toast.error(result.message);
        return false;
      }
    } finally {
      isDeleting.value = false;
    }
  };

  // 批量删除文件
  const batchDeleteFiles = async (items: ItemType[]): Promise<boolean> => {
    if (isDeleting.value || items.length === 0) return false;

    // 生成确认信息
    const itemNames =
      items.length <= 5
        ? items.map((item) => item.name).join(", ")
        : `${items
            .slice(0, 3)
            .map((item) => item.name)
            .join(", ")} 等 ${items.length} 个项目`;

    const confirmed = confirm(`确定要删除 ${items.length} 个项目吗？\n${itemNames}\n\n此操作不可撤销。`);
    if (!confirmed) return false;

    isDeleting.value = true;

    // 显示删除进度
    const progressToastId = toast.loading(`正在删除 ${items.length} 个项目...`);

    try {
      const fileIds = items.map((item) => item.id);
      const result = await callDeleteAPI(fileIds);

      toast.dismiss(progressToastId);

      if (result.success) {
        // 使用后端返回的成功消息，如果没有则使用默认格式
        const successMessage = result.message || `删除完成！成功删除 ${items.length} 个项目`;
        toast.success(successMessage);
        return true;
      } else {
        // 使用后端返回的错误消息
        toast.error(result.message);
        return false;
      }
    } catch (error) {
      toast.dismiss(progressToastId);
      console.error("批量删除失败:", error);
      const errorMessage = error instanceof Error ? error.message : "批量删除失败";
      toast.error(errorMessage);
      return false;
    } finally {
      isDeleting.value = false;
    }
  };

  // 通用删除函数 - 自动判断单个还是批量
  const deleteFiles = async (items: ItemType | ItemType[]): Promise<boolean> => {
    const itemArray = Array.isArray(items) ? items : [items];

    if (itemArray.length === 0) {
      toast.warning("请选择要删除的文件");
      return false;
    }

    if (itemArray.length === 1) {
      return await deleteSingleFile(itemArray[0]);
    } else {
      return await batchDeleteFiles(itemArray);
    }
  };

  return {
    isDeleting,
    deleteSingleFile,
    batchDeleteFiles,
    deleteFiles,
  };
}
