import { ref, computed } from "vue";
import { useRoute } from "vue-router";
import { toast } from "vue-sonner";
import filesApi, { type IFileMoveData } from "@/api/services/files";
import type { ItemType } from "@/types/files";

export function useFileMove() {
  // 实时获取当前路由的 categoryId
  const route = useRoute();
  const categoryId = computed(() => (route.query.categoryId as string) || "");
  const isMoving = ref(false);
  const showFolderPicker = ref(false);
  const pendingMoveItems = ref<ItemType[]>([]);

  // 调用移动接口（支持批量移动）
  const callMoveAPI = async (fileIds: (number | string)[] | number | string, targetDirId: number | string): Promise<{ success: boolean; message: string }> => {
    try {
      // 确保 fileIds 是数组格式
      const fileIdsArray = Array.isArray(fileIds) ? fileIds : [fileIds];

      const moveData: IFileMoveData = {
        category_id: categoryId.value,
        file_ids: fileIdsArray, // 现在支持数组
        target_dir_id: targetDirId,
      };

      console.log("🚀 调用批量移动API:", {
        fileIds: fileIdsArray,
        targetDirId,
        categoryId: categoryId.value,
      });

      const response = await filesApi.moveFiles(moveData); // 使用新的 moveFiles API

      if (response.code === 0) {
        console.log("✅ 批量移动成功");
        // 成功时使用后端返回的消息，如果没有则使用默认消息
        return { success: true, message: response.msg || "移动成功" };
      } else {
        console.error("❌ 批量移动失败:", response.msg);
        // 失败时使用后端返回的消息，如果没有则使用默认消息
        return { success: false, message: response.msg || "移动失败" };
      }
    } catch (error) {
      console.error("❌ 移动API调用失败:", error);
      const errorMessage = error instanceof Error ? error.message : "移动失败";
      return { success: false, message: errorMessage };
    }
  };

  // 移动文件（统一处理单个和批量，使用新的批量API）
  const moveFiles = async (items: ItemType | ItemType[], targetDirId: number | string): Promise<boolean> => {
    if (isMoving.value) return false;

    const itemArray = Array.isArray(items) ? items : [items];
    if (itemArray.length === 0) {
      toast.warning("请选择要移动的文件");
      return false;
    }

    isMoving.value = true;

    // 显示移动进度
    const isMultiple = itemArray.length > 1;
    const progressToastId = isMultiple ? toast.loading(`正在移动 ${itemArray.length} 个项目...`) : toast.loading(`正在移动 "${itemArray[0].name}"...`);

    try {
      // 提取所有文件ID
      const fileIds = itemArray.map((item) => item.id);

      // 调用批量移动API（一次性移动所有文件）
      const result = await callMoveAPI(fileIds, targetDirId);

      toast.dismiss(progressToastId);

      if (result.success) {
        // 使用后端返回的成功消息，如果没有则使用默认格式
        let successMessage = result.message;
        if (!successMessage) {
          successMessage = isMultiple ? `移动完成！成功移动 ${itemArray.length} 个项目` : `已移动 "${itemArray[0].name}"`;
        }
        toast.success(successMessage);
        return true;
      } else {
        // 使用后端返回的错误消息
        toast.error(result.message);
        return false;
      }
    } catch (error) {
      toast.dismiss(progressToastId);
      console.error("移动失败:", error);
      const errorMessage = error instanceof Error ? error.message : "移动失败";
      toast.error(errorMessage);
      return false;
    } finally {
      isMoving.value = false;
    }
  };

  // 开始移动流程 - 显示文件夹选择器
  const startMove = (items: ItemType | ItemType[]) => {
    const itemArray = Array.isArray(items) ? items : [items];

    if (itemArray.length === 0) {
      toast.warning("请选择要移动的文件");
      return;
    }

    pendingMoveItems.value = itemArray;
    showFolderPicker.value = true;
  };

  // 处理文件夹选择确认
  const handleFolderSelected = async (targetDirId: number | string): Promise<boolean> => {
    if (pendingMoveItems.value.length === 0) {
      toast.warning("没有要移动的文件");
      return false;
    }

    const items = pendingMoveItems.value;

    // 使用统一的移动函数
    const success = await moveFiles(items, targetDirId);

    // 清理状态
    pendingMoveItems.value = [];
    showFolderPicker.value = false;

    return success;
  };

  // 取消移动
  const cancelMove = () => {
    pendingMoveItems.value = [];
    showFolderPicker.value = false;
  };

  return {
    isMoving,
    showFolderPicker,
    pendingMoveItems,
    startMove,
    moveFiles,
    handleFolderSelected,
    cancelMove,
  };
}
