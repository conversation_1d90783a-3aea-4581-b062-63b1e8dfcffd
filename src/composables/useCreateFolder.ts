import { ref } from "vue";
import { toast } from "vue-sonner";
import filesApi from "@/api/services/files";
import type { ICreateFolderData } from "@/api/services/files";

/**
 * 文件夹创建 composable
 * 封装文件夹创建的业务逻辑，包括API调用、错误处理和状态管理
 */
export function useCreateFolder() {
  // 状态管理
  const isCreating = ref(false);
  const error = ref<string | null>(null);

  /**
   * 验证文件夹名称
   */
  const validateFolderName = (name: string): { valid: boolean; message?: string } => {
    const trimmedName = name.trim();

    // 检查是否为空
    if (!trimmedName) {
      return { valid: false, message: "文件夹名称不能为空" };
    }

    // 检查文件夹名长度（一般限制在255个字符以内）
    if (trimmedName.length > 255) {
      return { valid: false, message: "文件夹名称过长，请控制在255个字符以内" };
    }

    // 检查是否包含非法字符（Windows和Unix通用的非法字符）
    const invalidChars = /[<>:"/\\|?*\x00-\x1f]/;
    if (invalidChars.test(trimmedName)) {
      return { valid: false, message: "文件夹名称包含非法字符" };
    }

    // 检查是否以点开头或结尾（某些系统不支持）
    if (trimmedName.startsWith(".") || trimmedName.endsWith(".")) {
      return { valid: false, message: "文件夹名称不能以点开头或结尾" };
    }

    return { valid: true };
  };

  /**
   * 创建文件夹
   */
  const createFolder = async (folderName: string, categoryId: string | number, parentId: string | number): Promise<{ success: boolean; message: string; data?: any }> => {
    // 验证文件夹名称
    const validation = validateFolderName(folderName);
    if (!validation.valid) {
      return { success: false, message: validation.message || "文件夹名称无效" };
    }

    isCreating.value = true;
    error.value = null;

    try {
      const createData: ICreateFolderData = {
        category_id: categoryId,
        parent_id: parentId,
        folder_name: folderName.trim(),
      };

      const response = await filesApi.createFolder(createData);

      if (response.code === 0) {
        // 成功时使用后端返回的消息，如果没有则使用默认消息
        const successMessage = response.msg || "文件夹创建成功";
        return {
          success: true,
          message: successMessage,
          data: response.data,
        };
      } else {
        // 失败时使用后端返回的消息，如果没有则使用默认消息
        const errorMessage = response.msg || "文件夹创建失败";
        error.value = errorMessage;
        return { success: false, message: errorMessage };
      }
    } catch (err) {
      console.error("创建文件夹API调用失败:", err);
      const errorMessage = err instanceof Error ? err.message : "文件夹创建失败";
      error.value = errorMessage;
      return { success: false, message: errorMessage };
    } finally {
      isCreating.value = false;
    }
  };

  /**
   * 创建文件夹并显示提示
   */
  const createFolderWithToast = async (folderName: string, categoryId: string | number, parentId: string | number): Promise<boolean> => {
    const result = await createFolder(folderName, categoryId, parentId);

    if (result.success) {
      toast.success(result.message);
      return true;
    } else {
      toast.error(result.message);
      return false;
    }
  };

  /**
   * 重置状态
   */
  const resetState = () => {
    isCreating.value = false;
    error.value = null;
  };

  return {
    // 状态
    isCreating,
    error,

    // 方法
    validateFolderName,
    createFolder,
    createFolderWithToast,
    resetState,
  };
}
