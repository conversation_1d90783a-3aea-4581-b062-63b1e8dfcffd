<template>
  <div class="p-4 space-y-3 border-b">
    <h5 class="text-sm font-medium text-muted-foreground">基础信息</h5>

    <div class="space-y-2">
      <!-- 文件大小或项目数 -->
      <div class="flex justify-between">
        <span class="text-sm text-muted-foreground">
          {{ item.type === 'folder' ? '包含项目' : '文件大小' }}
        </span>
        <span class="text-sm font-medium">
          {{ getFileSize(item) }}
        </span>
      </div>

      <!-- 上传人 -->
      <div v-if="item.uploader" class="flex justify-between">
        <span class="text-sm text-muted-foreground">上传人</span>
        <span class="text-sm font-medium">{{ item.uploader }}</span>
      </div>

      <!-- 修改时间 -->
      <div v-if="item.update_time" class="flex justify-between">
        <span class="text-sm text-muted-foreground">修改时间</span>
        <span class="text-sm font-medium">{{ item.update_time }}</span>
      </div>

      <!-- 创建时间 -->
      <div v-if="item.create_time" class="flex justify-between">
        <span class="text-sm text-muted-foreground">创建时间</span>
        <span class="text-sm font-medium">{{ item.create_time }}</span>
      </div>

      <!-- 路径 -->
      <div v-if="item.path" class="flex justify-between">
        <span class="text-sm text-muted-foreground">路径</span>
        <span class="font-mono text-sm font-medium break-all">{{ item.path }}</span>
      </div>

      <!-- ID -->
      <div class="flex justify-between">
        <span class="text-sm text-muted-foreground">ID</span>
        <span class="font-mono text-sm font-medium">{{ item.id }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ItemType, FileItemType } from '@/types/files'

// Props
defineProps<{
  item: ItemType
}>()

// 获取文件大小显示
const getFileSize = (item: ItemType): string => {
  if (item.type === 'folder') {
    return '/'
  }

  const fileItem = item as FileItemType
  // 优先使用后端提供的人类可读格式（确保不是空字符串）
  if (fileItem.size_human && fileItem.size_human.trim() !== '') {
    return fileItem.size_human
  }
  // 回退到原有的格式化大小
  return fileItem.size || '未知'
}


</script>