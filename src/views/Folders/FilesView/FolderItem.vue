<template>
  <div class="folder-item" :class="{ 'selected': isSelected }" :data-id="dataId || item.id" @click="handleClick"
    @dblclick="handleDoubleClick" @contextmenu.prevent="handleContextMenu">
    <!-- 选择框 -->
    <div class="select-checkbox" v-if="showCheckbox">
      <Checkbox :model-value="isSelected" @click.stop @update:model-value="handleSelect" />
    </div>

    <!-- 文件夹图标区域 - 支持封面显示 -->
    <div class="w-[100px] h-[100px] flex justify-center items-center relative">
      <!-- 基础文件夹图标 -->
      <Folder class="w-28 h-28 text-blue-500" />

      <!-- 视频封面叠加层 - 优先级最高 -->
      <div v-if="item.thumbnailVideo && item.thumbnailVideo.trim()"
        class="flex absolute inset-0 justify-center items-center">
        <div class="overflow-hidden relative w-20 h-20 rounded-md border shadow-md border-white/20">
          <video ref="videoRef" :src="item.thumbnailVideo" class="object-cover w-full h-full" muted loop
            preload="metadata" @error="handleVideoError" @mouseenter="handleVideoMouseEnter"
            @mouseleave="handleVideoMouseLeave" />
        </div>
      </div>

      <!-- 图片封面叠加层 - 当没有视频时显示 -->
      <div v-else-if="item.thumbnailMedium && item.thumbnailMedium.trim()"
        class="flex absolute inset-0 justify-center items-center">
        <div class="overflow-hidden relative w-20 h-20 rounded-md border shadow-md border-white/20">
          <img :src="item.thumbnailMedium" :alt="`${item.name} 封面`" class="object-cover w-full h-full"
            @error="handleImageError" />
        </div>
      </div>
    </div>

    <!-- 文件夹名 - 支持重命名 -->
    <div v-if="!renameState.isRenaming.value" class="w-full">
      <Tooltip>
        <TooltipTrigger as-child>
          <div class="px-1 text-sm font-medium text-center truncate" :title="item.name">
            {{ item.name }}
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>{{ item.name }}</p>
        </TooltipContent>
      </Tooltip>
    </div>
    <div v-else class="mb-1 w-full">
      <RenameInput v-model="renameState.newName.value" :input-ref="renameState.renameInputRef"
        input-class="flex-1 px-2 py-1 h-auto text-sm text-center" @confirm="handleRenameConfirm"
        @cancel="handleRenameCancel" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Folder } from 'lucide-vue-next'
import { useItemRename } from './useRename'
import { Checkbox } from '@/components/ui/checkbox'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'
import RenameInput from './RenameInput.vue'

// Props
const props = defineProps<{
  item: {
    id: string
    name: string
    type: 'folder'
    itemCount: number
    create_time?: string
    update_time?: string
    path?: string
    thumbnailMedium?: string
    thumbnail_video?: string
    // 允许其他动态属性
    [key: string]: any
  }
  isSelected?: boolean
  showCheckbox?: boolean
  dataId?: string
  isRenaming?: boolean
}>()

// Emits
const emit = defineEmits<{
  click: [item: typeof props.item]
  dblclick: [item: typeof props.item]
  contextmenu: [event: MouseEvent, item: typeof props.item]
  select: [itemId: string, selected: boolean]
  rename: [itemId: string, newName: string]
  renameCancel: [itemId: string]
}>()

// 使用重命名 composable
const renameState = useItemRename(props)



// 视频播放控制
const videoRef = ref<HTMLVideoElement | null>(null)

// 视频hover事件处理
const handleVideoMouseEnter = () => {
  if (videoRef.value) {
    videoRef.value.play().catch(() => {
      // 忽略播放失败的错误，可能是由于浏览器策略限制
    })
  }
}

const handleVideoMouseLeave = () => {
  if (videoRef.value) {
    videoRef.value.pause()
    videoRef.value.currentTime = 0 // 重置到开始位置
  }
}

// 重命名事件处理
const handleRenameConfirm = () => {
  const trimmedName = renameState.newName.value.trim()
  if (trimmedName && trimmedName !== props.item.name) {
    emit('rename', props.item.id, trimmedName)
  } else {
    handleRenameCancel()
  }
  renameState.cancelRename()
}

const handleRenameCancel = () => {
  emit('renameCancel', props.item.id)
  renameState.cancelRename()
}

// 事件处理
const handleClick = () => {
  if (!renameState.isRenaming.value) {
    emit('click', props.item)
  }
}

const handleDoubleClick = () => {
  if (!renameState.isRenaming.value) {
    emit('dblclick', props.item)
  }
}

const handleContextMenu = (event: MouseEvent) => {
  emit('contextmenu', event, props.item)
}

const handleSelect = (selected: boolean | 'indeterminate') => {
  emit('select', props.item.id, selected === true)
}

// 图片加载错误处理
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  if (img) {
    // 隐藏封面图片，显示默认文件夹图标
    const coverContainer = img.closest('.absolute') as HTMLElement
    if (coverContainer) {
      coverContainer.style.display = 'none'
    }
  }
}

// 视频加载错误处理
const handleVideoError = (event: Event) => {
  const video = event.target as HTMLVideoElement
  if (video) {
    // 隐藏视频封面，回退到图片封面或默认图标
    const coverContainer = video.closest('.absolute') as HTMLElement
    if (coverContainer) {
      coverContainer.style.display = 'none'
    }
  }
}
</script>

<style scoped>
.folder-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid hsl(var(--border));
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  background-color: hsl(var(--card));
  contain: layout style paint;
  position: relative;
  overflow: hidden;
  will-change: transform, background-color, border-color, box-shadow;
}

.folder-item.selected {
  background-color: hsl(var(--primary) / 0.1);
  border-color: hsl(var(--primary));
}

.select-checkbox {
  position: absolute;
  top: 0.5rem;
  left: 0.5rem;
  z-index: 10;
}

.folder-item:hover {
  border-color: hsl(var(--primary));
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.folder-item:active {
  transform: translateY(-1px);
  transition-duration: 0.1s;
}

.folder-item p {
  transition: color 0.2s ease;
  margin: 0;
  line-height: 1.2;
}

/* 封面文件夹样式 */
.folder-item:hover .absolute img,
.folder-item:hover .absolute video {
  transform: scale(1.05);
  transition: transform 0.2s ease;
}

.folder-item .absolute img,
.folder-item .absolute video {
  transition: transform 0.2s ease;
}

/* 封面容器的阴影效果 */
.folder-item .absolute>div {
  transition: box-shadow 0.2s ease;
}

.folder-item:hover .absolute>div {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 视频封面特定样式 */
.folder-item .absolute video {
  cursor: pointer;
}

/* 确保视频在暂停状态下显示第一帧 */
.folder-item .absolute video:not(:hover) {
  object-position: center;
}
</style>