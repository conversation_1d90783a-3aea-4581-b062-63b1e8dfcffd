import { ref, nextTick, watch, computed } from "vue";
import { useRoute } from "vue-router";
import { toast } from "vue-sonner";
import filesApi, { type IFileRenameData } from "@/api/services/files";
import type { ItemType } from "@/types/files";

// 检查元素是否为有效的输入元素
const isValidInputElement = (element: any): element is HTMLInputElement => {
  return element && typeof element === "object" && element.nodeType === Node.ELEMENT_NODE && typeof element.focus === "function" && typeof element.select === "function";
};

// 共用的输入框焦点和选择逻辑
export const focusAndSelectInput = (inputRef: any) => {
  nextTick(() => {
    const inputElement = inputRef.value;
    if (isValidInputElement(inputElement)) {
      try {
        inputElement.focus();
        inputElement.select();
      } catch (error) {
        console.warn("Failed to focus input element:", error);
      }
    }
  });
};

// 验证文件名
const validateFileName = (newName: string, currentName: string, existingItems?: ItemType[]): { valid: boolean; message?: string } => {
  const trimmedName = newName.trim();
  console.log("validateFileName - newName:", newName, "currentName:", currentName, "trimmedName:", trimmedName);

  // 检查是否为空
  if (!trimmedName) {
    return { valid: false, message: "文件名不能为空" };
  }

  // 检查是否与当前名称相同
  if (trimmedName === currentName) {
    return { valid: false, message: "文件名未发生变化" };
  }

  // 检查文件名长度（一般限制在255个字符以内）
  if (trimmedName.length > 255) {
    return { valid: false, message: "文件名过长，请控制在255个字符以内" };
  }

  // 检查是否包含非法字符（Windows和Unix通用的非法字符）
  const invalidChars = /[<>:"/\\|?*\x00-\x1f]/;
  if (invalidChars.test(trimmedName)) {
    return { valid: false, message: "文件名包含非法字符" };
  }

  // 检查是否以点开头或结尾（某些系统不支持）
  if (trimmedName.startsWith(".") || trimmedName.endsWith(".")) {
    return { valid: false, message: "文件名不能以点开头或结尾" };
  }

  // 检查是否与现有文件重名
  if (existingItems) {
    const existingItem = existingItems.find((item) => item.name.toLowerCase() === trimmedName.toLowerCase());
    if (existingItem) {
      return { valid: false, message: "已存在相同名称的文件或文件夹" };
    }
  }

  return { valid: true };
};

// 调用重命名API
const callRenameAPI = async (fileId: number | string, newName: string, categoryId: string): Promise<{ success: boolean; message: string }> => {
  try {
    const renameData: IFileRenameData = {
      category_id: categoryId,
      file_id: fileId,
      new_name: newName.trim(),
    };

    const response = await filesApi.renameFile(renameData);

    if (response.code === 0) {
      // 成功时使用后端返回的消息，如果没有则使用默认消息
      return { success: true, message: response.msg || "重命名成功" };
    } else {
      // 失败时使用后端返回的消息，如果没有则使用默认消息
      return { success: false, message: response.msg || "重命名失败" };
    }
  } catch (error) {
    console.error("重命名API调用失败:", error);
    const errorMessage = error instanceof Error ? error.message : "重命名失败";
    return { success: false, message: errorMessage };
  }
};

/**
 * 基础重命名hook - 提供通用的重命名状态和方法
 */
export function useBaseRename() {
  // 实时获取当前路由的 categoryId
  const route = useRoute();
  const categoryId = computed(() => (route.query.categoryId as string) || "");

  const newName = ref("");
  const renameInputRef = ref<HTMLInputElement>();
  const isRenaming = ref(false);

  // 基础的开始重命名方法
  const baseStartRename = (currentName: string) => {
    newName.value = currentName;
    focusAndSelectInput(renameInputRef);
  };

  // 基础的取消重命名方法
  const baseCancelRename = () => {
    newName.value = "";
  };

  // 执行重命名API调用
  const executeRename = async (item: ItemType, newNameValue: string, existingItems?: ItemType[]): Promise<boolean> => {
    if (isRenaming.value) return false;
    console.log("executeRename", item, newNameValue, existingItems);

    // 验证文件名 - 使用传入的新名称和原始项目名称
    const validation = validateFileName(newNameValue, item.name, existingItems);
    if (!validation.valid) {
      toast.error(validation.message || "文件名验证失败");
      return false;
    }

    isRenaming.value = true;

    try {
      const result = await callRenameAPI(item.id, newNameValue.trim(), categoryId.value);

      if (result.success) {
        // 使用后端返回的成功消息，如果没有则使用默认格式
        const successMessage = result.message || `已重命名为 "${newNameValue.trim()}"`;
        toast.success(successMessage);
        return true;
      } else {
        // 使用后端返回的错误消息
        toast.error(result.message);
        return false;
      }
    } catch (error) {
      console.error("重命名失败:", error);
      const errorMessage = error instanceof Error ? error.message : "重命名失败";
      toast.error(errorMessage);
      return false;
    } finally {
      isRenaming.value = false;
    }
  };

  return {
    newName,
    renameInputRef,
    isRenaming,
    baseStartRename,
    baseCancelRename,
    executeRename,
  };
}

/**
 * 列表视图重命名hook - 用于列表视图的重命名功能
 */
export function useListViewRename() {
  const { newName, renameInputRef, isRenaming, baseStartRename, baseCancelRename, executeRename } = useBaseRename();
  const renamingItemId = ref<string | null>(null);

  // 开始重命名
  const startRename = (itemId: string, currentName: string) => {
    renamingItemId.value = itemId;
    baseStartRename(currentName);
  };

  // 取消重命名
  const cancelRename = () => {
    renamingItemId.value = null;
    baseCancelRename();
  };

  // 确认重命名
  const confirmRename = async (item: ItemType, existingItems?: ItemType[]): Promise<boolean> => {
    const success = await executeRename(item, newName.value, existingItems);
    if (success) {
      renamingItemId.value = null;
      baseCancelRename();
    }
    return success;
  };

  return {
    renamingItemId,
    newName,
    renameInputRef,
    isRenaming,
    startRename,
    cancelRename,
    confirmRename,
  };
}

/**
 * 单项重命名hook - 用于单个项目的重命名功能
 * @param props 包含项目信息和外部重命名状态的配置
 */
export function useItemRename(props: { isRenaming?: boolean; item: { id: string; name: string } }) {
  const { newName, renameInputRef, isRenaming: apiRenaming, baseStartRename, baseCancelRename, executeRename } = useBaseRename();
  const isRenaming = ref(false);

  // 监听外部重命名状态变化
  watch(
    () => props.isRenaming,
    (shouldRename) => {
      if (shouldRename && !isRenaming.value) {
        startRename();
      } else if (!shouldRename && isRenaming.value) {
        cancelRename();
      }
    }
  );

  // 开始重命名
  const startRename = () => {
    isRenaming.value = true;
    baseStartRename(props.item.name);
  };

  // 取消重命名
  const cancelRename = () => {
    isRenaming.value = false;
    baseCancelRename();
  };

  // 确认重命名
  const confirmRename = async (existingItems?: ItemType[]): Promise<boolean> => {
    const success = await executeRename(props.item as ItemType, newName.value, existingItems);
    if (success) {
      isRenaming.value = false;
      baseCancelRename();
    }
    return success;
  };

  return {
    isRenaming,
    newName,
    renameInputRef,
    apiRenaming,
    startRename,
    cancelRename,
    confirmRename,
  };
}
