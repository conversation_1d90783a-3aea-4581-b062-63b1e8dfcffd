<template>
  <div class="space-y-4">
    <!-- 工具栏 -->
    <div class="flex justify-between items-center">
      <div class="flex gap-4 items-center">
        <!-- 搜索框 -->
        <div class="relative w-64">
          <Search class="absolute left-3 top-1/2 w-4 h-4 transform -translate-y-1/2 text-muted-foreground" />
          <Input v-model="searchQuery" placeholder="搜索游戏项目..." class="pl-10" />
        </div>


      </div>

      <!-- 操作按钮 -->
      <div class="flex gap-2 items-center">
        <Button variant="outline" size="sm" @click="refresh" :disabled="state.loading">
          <RefreshCw class="mr-1 w-4 h-4" :class="{ 'animate-spin': state.loading }" />
          刷新
        </Button>
        <Button @click="openCreateDialog">
          <Plus class="mr-1 w-4 h-4" />
          新建项目
        </Button>
      </div>
    </div>

    <!-- 表格 -->
    <div class="rounded-lg border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead class="w-20 text-center">序号</TableHead>
            <TableHead class="text-center">
              <div class="flex justify-center items-center">
                游戏项目
              </div>
            </TableHead>
            <TableHead class="text-center">
              <div class="flex justify-center items-center">
                更新人
              </div>
            </TableHead>
            <TableHead class="text-center">
              <div class="flex justify-center items-center">
                更新时间
              </div>
            </TableHead>
            <TableHead class="w-32 text-center">操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-if="state.loading">
            <TableCell colspan="5" class="py-8 text-center">
              <div class="flex justify-center items-center">
                <RefreshCw class="mr-2 w-4 h-4 animate-spin" />
                加载中...
              </div>
            </TableCell>
          </TableRow>
          <TableRow v-else-if="state.gameProjects.length === 0">
            <TableCell colspan="5" class="py-8 text-center text-muted-foreground">
              暂无数据
            </TableCell>
          </TableRow>
          <TableRow v-else v-for="project in state.gameProjects" :key="project.id">
            <TableCell class="text-center">{{ project.id }}</TableCell>
            <TableCell class="text-center">{{ project.name }}</TableCell>
            <TableCell class="text-center">{{ project.updatedBy }}</TableCell>
            <TableCell class="text-center">{{ formatDateTime(project.updatedAt) }}</TableCell>
            <TableCell class="text-center">
              <div class="flex gap-2 justify-center items-center">
                <Button variant="outline" size="sm" @click="openEditDialog(project)">
                  <Edit class="w-4 h-4" />
                </Button>
                <Button variant="destructive" size="sm" @click="handleDelete(project)">
                  <Trash2 class="w-4 h-4" />
                </Button>
              </div>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>



    <!-- 创建/编辑对话框 -->
    <GameProjectFormDialog v-model:open="isCreateDialogOpen" title="新建游戏项目" @submit="handleCreateAndUpdate" />

    <GameProjectFormDialog v-model:open="isEditDialogOpen" title="编辑游戏项目" :initial-data="editingProject"
      @submit="handleCreateAndUpdate" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, watch } from 'vue'
import { Search, Plus, Edit, Trash2, RefreshCw } from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { useGameProjectsManagement } from './composables/useGameProjectsManagement'
import { useSearch } from '@/composables/useSearch'
import GameProjectFormDialog from './GameProjectFormDialog.vue'
import type { GameProject, CreateGameProjectParams, UpdateGameProjectParams } from '@/api/services/tags'

// 使用游戏项目管理 composable
const {
  state,
  editingProject,
  isEditDialogOpen,
  isCreateDialogOpen,
  fetchGameProjects,
  createGameProject,
  updateGameProject,
  deleteGameProject,
  search,
  openEditDialog,
  openCreateDialog,
  refresh,
} = useGameProjectsManagement()

// 使用统一搜索 composable
const { searchQuery, handleSearchInput } = useSearch({
  debounceDelay: 300,
  onSearch: (query: string) => {
    search(query)
  }
})

// 监听搜索查询变化，处理中文输入法问题
watch(searchQuery, (newQuery) => {
  handleSearchInput(newQuery)
}, { immediate: false })

// 格式化日期
function formatDateTime(dateString: string) {
  return dateString || '--';
}

// 事件处理
const handleCreateAndUpdate = async (data: CreateGameProjectParams | UpdateGameProjectParams) => {
  if ('id' in data) {
    await updateGameProject(data as UpdateGameProjectParams)
  } else {
    await createGameProject(data as CreateGameProjectParams)
  }
}

const handleDelete = async (project: GameProject) => {
  if (confirm(`确定要删除游戏项目"${project.name}"吗？`)) {
    await deleteGameProject(project.id)
  }
}

// 生命周期
onMounted(() => {
  fetchGameProjects()
})

// 搜索功能的定时器清理现在由 useSearch composable 自动处理
</script>
