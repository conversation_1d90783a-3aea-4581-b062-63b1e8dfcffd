// 测试空文件夹上传功能
const { ipcMain } = require('electron');

// 模拟测试空文件夹拖拽
async function testEmptyFolderDrag() {
  console.log('🧪 开始测试空文件夹拖拽功能');
  
  // 模拟拖拽空文件夹的文件路径
  const testPaths = [
    '/tmp/test-empty-folders/empty1',
    '/tmp/test-empty-folders/empty2',
    '/tmp/test-empty-folders/folder-with-empty'
  ];
  
  try {
    // 模拟调用拖拽处理器
    const result = await new Promise((resolve, reject) => {
      // 这里需要实际的IPC调用，但在测试环境中我们可以直接调用处理函数
      // 实际测试需要在Electron环境中进行
      console.log('📁 测试路径:', testPaths);
      resolve({ success: true, message: '测试完成' });
    });
    
    console.log('✅ 测试结果:', result);
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 测试文件夹遍历功能
async function testFolderTraversal() {
  console.log('🧪 开始测试文件夹遍历功能');
  
  const fs = require('fs').promises;
  const path = require('path');
  
  // 模拟getAllFilesInDirectory函数
  async function getAllFilesInDirectory(dirPath, basePath) {
    const files = [];
    const baseDir = basePath || dirPath;

    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });

      // 如果目录为空，创建一个空文件夹条目
      if (entries.length === 0) {
        const relativePath = path.relative(baseDir, dirPath);
        
        files.push({
          path: dirPath,
          name: path.basename(dirPath),
          size: 0,
          isDirectory: true,
          isEmpty: true,
          relativePath: relativePath,
        });
        
        console.log(`📁 发现空文件夹: ${relativePath}`);
        return files;
      }

      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        const relativePath = path.relative(baseDir, fullPath);

        if (entry.isDirectory()) {
          // 递归处理子目录
          const subFiles = await getAllFilesInDirectory(fullPath, baseDir);
          files.push(...subFiles);
        } else if (entry.isFile()) {
          const stats = await fs.stat(fullPath);
          files.push({
            path: fullPath,
            name: entry.name,
            size: stats.size,
            isDirectory: false,
            relativePath: relativePath,
          });
        }
      }
    } catch (error) {
      console.error(`读取目录 ${dirPath} 失败:`, error);
    }

    return files;
  }
  
  // 测试空文件夹遍历
  const testDir = '/tmp/test-empty-folders';
  try {
    const result = await getAllFilesInDirectory(testDir);
    console.log('📊 遍历结果:');
    result.forEach(file => {
      if (file.isDirectory && file.isEmpty) {
        console.log(`  📁 空文件夹: ${file.relativePath}`);
      } else {
        console.log(`  📄 文件: ${file.relativePath} (${file.size} bytes)`);
      }
    });
  } catch (error) {
    console.error('❌ 遍历测试失败:', error);
  }
}

// 运行测试
async function runTests() {
  console.log('🚀 开始空文件夹上传功能测试');
  console.log('='.repeat(50));
  
  await testFolderTraversal();
  console.log('='.repeat(50));
  await testEmptyFolderDrag();
  
  console.log('✅ 所有测试完成');
}

// 如果直接运行此脚本
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testEmptyFolderDrag,
  testFolderTraversal,
  runTests
};
